import { useState, useEffect } from 'react';
import { getDecodedToken } from './getDecodedToken.js';

/**
 * Cached hook for decoded token to avoid redundant calls
 * Keeps existing business logic intact while reducing duplicate API calls
 */
export const useDecodedToken = (forceRefresh = false) => {
  const [decoded, setDecoded] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    let isMounted = true;

    const fetchToken = async () => {
      if (isLoading) return; // Prevent duplicate calls
      
      setIsLoading(true);
      setError(null);

      try {
        const decodedToken = await getDecodedToken(forceRefresh);
        if (isMounted) {
          setDecoded(decodedToken);
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error('Failed to decode token'));
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchToken();

    return () => {
      isMounted = false;
    };
  }, [forceRefresh, isLoading]);

  return { decoded, isLoading, error };
};
