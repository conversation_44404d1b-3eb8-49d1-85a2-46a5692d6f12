'use client';

import React, { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { useAuth } from '@minicardiac-client/apis';
import { getDecodedToken } from '@minicardiac-client/utilities';
import { FullPageLoader } from '@minicardiac-client/components';

interface RouteGuardProps {
  children: React.ReactNode;
  disableRedirect?: boolean;
}

/**
 * Simplified ProtectedRoute - Middleware handles authentication
 * This component only handles role-based redirects and email verification
 */
export const ProtectedRoute: React.FC<RouteGuardProps> = ({ children }) => {
  const { authState } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [decoded, setDecoded] = useState<any>(null);
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    const fetchToken = async () => {
      if (authState.isAuthenticated && !decoded && !isRedirecting) {
        try {
          const decodedToken = await getDecodedToken();
          setDecoded(decodedToken);
        } catch (error) {
          console.error('Failed to decode token:', error);
        }
      }
    };

    fetchToken();
  }, [authState.isAuthenticated, decoded, isRedirecting]);

  useEffect(() => {
    // Handle email verification and role-based redirects
    if (authState.isAuthenticated && decoded && !isRedirecting) {
      // Skip redirects for special paths (middleware handles these)
      if (pathname === '/dev' || pathname?.startsWith('/professional')) {
        return;
      }

      // Email verification redirect
      if (!decoded.email_verified) {
        setIsRedirecting(true);
        router.push(
          `/verify-otp?email=${encodeURIComponent(
            decoded.email || ''
          )}&name=${encodeURIComponent(decoded.name || '')}&accountType=${
            decoded.accountType || 'PUBLIC'
          }`
        );
        return;
      }

      // Role-based redirects
      const userRole = decoded?.role || decoded?.userRole;
      const isOnProfessionalRoute = pathname?.includes('/professional') || false;
      const isOnPatientRoute = pathname?.includes('/patient') || pathname?.includes('/feed') || false;

      if (userRole === 'PROFESSIONAL' && isOnPatientRoute) {
        setIsRedirecting(true);
        router.push('/professional/dashboard');
        return;
      }

      if (userRole === 'GENERAL_PUBLIC' && isOnProfessionalRoute) {
        setIsRedirecting(true);
        router.push('/feed');
        return;
      }
    }
  }, [authState.isAuthenticated, decoded, pathname, router, isRedirecting]);

  // Middleware handles auth, so we only show loading for internal redirects
  if (authState.isLoading || isRedirecting) {
    return <FullPageLoader open={true} />;
  }

  // Middleware ensures only authenticated users reach here
  return <>{children}</>;
};
/**
 * Simplified RedirectIfAuthenticated - Middleware handles most auth logic
 * This component only handles client-side redirect for authenticated users
 */
export const RedirectIfAuthenticated: React.FC<RouteGuardProps> = ({
  children,
  disableRedirect = false,
}) => {
  const { authState } = useAuth();
  const router = useRouter();
  const [hasRedirected, setHasRedirected] = useState(false);

  useEffect(() => {
    // Simple redirect for authenticated users - middleware handles the heavy lifting
    if (
      !authState.isLoading &&
      authState.isAuthenticated &&
      !disableRedirect &&
      !hasRedirected
    ) {
      setHasRedirected(true);
      router.push('/feed');
    }
  }, [authState.isLoading, authState.isAuthenticated, disableRedirect, hasRedirected, router]);

  // Show loading only if we're processing auth state
  if (authState.isLoading) {
    return disableRedirect ? <>{children}</> : <FullPageLoader open={true} />;
  }

  // Show children if user is not authenticated or redirect is disabled
  if (!authState.isAuthenticated) {
    return <>{children}</>;
  }

  // Don't render anything if authenticated (will redirect)
  return null;
};

/**
 * Simplified PatientProfileRedirect - Middleware handles auth
 * This component only handles profile completion flow
 */
export const PatientProfileRedirect: React.FC<RouteGuardProps> = ({
  children,
}) => {
  const { authState } = useAuth();
  const router = useRouter();
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    const handleRedirect = async () => {
      if (authState.isAuthenticated && !isRedirecting) {
        try {
          const decodedToken = await getDecodedToken();

          if (decodedToken?.currentStage === undefined) {
            setIsRedirecting(true);
            router.push(
              `/verify-otp?email=${encodeURIComponent(
                decodedToken?.email || ''
              )}&name=${encodeURIComponent(decodedToken?.name || '')}`
            );
          } else if (decodedToken?.currentStage === 'completed') {
            setIsRedirecting(true);
            router.push('/feed');
          }
        } catch (error) {
          console.error('Failed to decode token:', error);
        }
      }

      // Middleware handles unauthenticated users, but fallback for client-side
      if (!authState.isAuthenticated && !authState.isLoading && !isRedirecting) {
        setIsRedirecting(true);
        router.push('/signup/patient');
      }
    };

    if (!authState.isLoading) {
      handleRedirect();
    }
  }, [authState, router, isRedirecting]);

  if (authState.isLoading || isRedirecting) {
    return <FullPageLoader open={true} />;
  }

  return <>{children}</>;
};

/**
 * Simplified VerifyOTPRedirect - Middleware handles auth
 * This component only handles OTP verification flow
 */
export const VerifyOTPRedirect: React.FC<RouteGuardProps> = ({ children }) => {
  const { authState } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const isVerifyOtpPage = pathname?.startsWith('/verify-otp');
  const [isRedirecting, setIsRedirecting] = useState(false);

  useEffect(() => {
    const handleRedirect = async () => {
      if (authState.isAuthenticated && !isRedirecting) {
        try {
          const decodedToken = await getDecodedToken();

          if (decodedToken?.currentStage === undefined) {
            setIsRedirecting(true);
            router.push(
              `/verify-otp?email=${encodeURIComponent(
                decodedToken?.email || ''
              )}&name=${encodeURIComponent(decodedToken?.name || '')}`
            );
          }
          // Note: Completed stage redirect removed as per original comment
        } catch (error) {
          console.error('Failed to decode token:', error);
        }
      }
    };

    if (!authState.isLoading) {
      handleRedirect();
    }
  }, [authState, router, isRedirecting]);

  // Don't show loader on verify-otp page as it handles its own loading state
  if (authState.isLoading && !isVerifyOtpPage && !isRedirecting) {
    return <FullPageLoader open={true} />;
  }

  return <>{children}</>;
};
