'use client';

import { useEffect } from 'react';
import { useAuth, verifySession } from '@minicardiac-client/apis';
import { useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';

/**
 * Simplified SessionVerifier that works with centralized middleware
 * Middleware handles route-level authentication, this handles session sync
 */
const useSessionVerification = () => {
  const { authState } = useAuth();
  const router = useRouter();

  const sessionQuery = useQuery({
    queryKey: ['auth', 'session'],
    queryFn: async () => {
      if (!authState.isAuthenticated || !authState.user) {
        throw new Error('User not authenticated');
      }
      return await verifySession();
    },
    enabled: authState.isAuthenticated && !!authState.user,
    retry: 1, 
    refetchInterval: 15 * 60 * 1000, 
    refetchOnWindowFocus: false,
    refetchOnMount: false // Prevent unnecessary refetches
  });

  useEffect(() => {
    // Handle session errors - let middleware handle redirects
    if (sessionQuery.isError && authState.isAuthenticated) {
      console.warn('Session verification failed, middleware will handle redirect');
      // Force a page refresh to trigger middleware auth check
      router.refresh();
    }
  }, [sessionQuery.isError, authState.isAuthenticated, router]);

  return {
    isLoading: sessionQuery.isLoading,
    isError: sessionQuery.isError,
    data: sessionQuery.data
  };
};

export const SessionVerifier = () => {
  useSessionVerification();
  return null;
};

export default SessionVerifier;


