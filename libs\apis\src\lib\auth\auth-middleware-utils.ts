/**
 * Utilities for working with middleware auth status
 * These help components understand auth state from middleware headers
 */

import { useEffect, useState } from 'react';

export type AuthStatus = 'authenticated' | 'unauthenticated' | 'unknown';
export type RouteType = 'protected' | 'public' | 'guest-only' | 'unknown';

/**
 * Hook to read auth status from middleware headers
 * This provides a way for components to understand middleware decisions
 */
export const useMiddlewareAuthStatus = () => {
  const [authStatus, setAuthStatus] = useState<AuthStatus>('unknown');
  const [routeType, setRouteType] = useState<RouteType>('unknown');

  useEffect(() => {
    // Read auth status from document meta tags (set by middleware)
    const authMeta = document.querySelector('meta[name="x-auth-status"]');
    const routeMeta = document.querySelector('meta[name="x-route-type"]');

    if (authMeta) {
      setAuthStatus(authMeta.getAttribute('content') as AuthStatus || 'unknown');
    }

    if (routeMeta) {
      setRouteType(routeMeta.getAttribute('content') as RouteType || 'unknown');
    }
  }, []);

  return {
    authStatus,
    routeType,
    isAuthenticated: authStatus === 'authenticated',
    isProtectedRoute: routeType === 'protected',
    isPublicRoute: routeType === 'public',
    isGuestOnlyRoute: routeType === 'guest-only'
  };
};

/**
 * Check if current route requires authentication based on pathname
 * This mirrors the middleware logic for client-side checks
 */
export const isProtectedRoute = (pathname: string): boolean => {
  const PROTECTED_ROUTES = [
    '/feed',
    '/profile',
    '/dashboard',
    '/settings'
  ];

  // Remove locale prefix for route checking
  const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}/, '') || '/';
  
  return PROTECTED_ROUTES.some(route => 
    pathWithoutLocale === route || pathWithoutLocale.startsWith(route + '/')
  );
};

/**
 * Check if current route is public based on pathname
 * This mirrors the middleware logic for client-side checks
 */
export const isPublicRoute = (pathname: string): boolean => {
  const PUBLIC_ROUTES = [
    '/signin',
    '/signup-type',
    '/signup/professional',
    '/signup/organization',
    '/signup/patient',
    '/verify-otp',
    '/dev',
    '/',
    '/about',
    '/contact'
  ];

  // Remove locale prefix for route checking
  const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}/, '') || '/';
  
  // Special cases for dev and professional routes
  if (pathWithoutLocale === '/dev' || pathWithoutLocale.startsWith('/professional')) {
    return true;
  }
  
  return PUBLIC_ROUTES.some(route => 
    pathWithoutLocale === route || pathWithoutLocale.startsWith(route + '/')
  );
};

/**
 * Check if authenticated users should be redirected from this route
 * This mirrors the middleware logic for client-side checks
 */
export const isGuestOnlyRoute = (pathname: string): boolean => {
  const GUEST_ONLY_ROUTES = [
    '/signin',
    '/signup-type',
    '/signup/professional',
    '/signup/organization',
    '/signup/patient'
  ];

  // Remove locale prefix for route checking
  const pathWithoutLocale = pathname.replace(/^\/[a-z]{2}/, '') || '/';
  
  return GUEST_ONLY_ROUTES.some(route => 
    pathWithoutLocale === route || pathWithoutLocale.startsWith(route + '/')
  );
};
