import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { AuthState } from './types.js';
import { RegisterUserRequest, RegisterUserResponse } from './types.js';
import {
  registerUser,
  verifyOtp,
  regenerateOtp,
  sessionLogin,
  verifySession,
  logout,
  getOwnProfile,
} from './auth-api.js';
import { auth } from '../firebase/firebase-client.js';

import {
  signInWithEmailAndPassword,
  signInWithCustomToken,
  signOut,
  onAuthStateChanged,
} from 'firebase/auth';
import { useEffect, useState } from 'react';
import { resetAuthMode } from '../http-client.js';
import { jwtDecode } from 'jwt-decode';
import { establishSession } from './auth-utils.js';

export const authQueryKeys = {
  all: ['auth'] as const,
  session: () => [...authQueryKeys.all, 'session'] as const,
  user: () => [...authQueryKeys.all, 'user'] as const,
  registration: () => [...authQueryKeys.all, 'registration'] as const,
  verification: () => [...authQueryKeys.all, 'verification'] as const,
};

export const useRegisterUser = () => {
  const queryClient = useQueryClient();

  return useMutation<RegisterUserResponse, unknown, RegisterUserRequest>({
    mutationFn: async (data: RegisterUserRequest) => {
      const result = await registerUser(data);
      // After successful signup, get ID token and perform session login
      if (auth?.currentUser) {
        const idToken = await auth.currentUser.getIdToken();
        if (idToken) {
          await sessionLogin({ idToken });
        }
      }
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: authQueryKeys.all });
    },
  });
};

export const useVerifyOtp = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: verifyOtp,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: authQueryKeys.all });
    },
  });
};

export const useRegenerateOtp = () => {
  return useMutation({
    mutationFn: regenerateOtp,
  });
};

export const useFirebaseAuth = () => {
  const [state, setState] = useState<AuthState>({
    isLoading: true,
    isAuthenticated: false,
    user: null,
    error: null,
  });

  useEffect(() => {
    // If auth is undefined, set state to not authenticated
    if (!auth) {
      setState({
        isLoading: false,
        isAuthenticated: false,
        user: null,
        error: {
          message: 'Firebase auth is not initialized',
          code: 'auth/not-initialized',
        },
      });
      // Return a no-op cleanup function
      return () => {
        /* No cleanup needed */
      };
    }

    const unsubscribe = onAuthStateChanged(
      auth,
      async (user) => {
        const idToken = await user?.getIdToken();

        if (!idToken || typeof idToken !== 'string' || !idToken.includes('.')) {
          setState({
            isLoading: false,
            isAuthenticated: false,
            user: null,
            error: {
              message: 'Invalid token format',
              code: 'auth/invalid-token',
            },
          });
          return;
        }

        const decodedToken: any = jwtDecode(idToken);

        setState({
          isLoading: false,
          isAuthenticated: !!user,
          user: user
            ? {
                uid: user.uid,
                email: user.email,
                displayName: user.displayName,
                photoURL: user.photoURL,
                emailVerified: user.emailVerified,
                currentStage: decodedToken.currentStage,
              }
            : null,
          error: null,
        });
      },
      (error) => {
        setState({
          isLoading: false,
          isAuthenticated: false,
          user: null,
          error: { message: error.message, code: 'auth/unknown' },
        });
      }
    );

    return () => unsubscribe();
  }, []);

  return state;
};

export const useEmailPasswordSignIn = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      email,
      password,
    }: {
      email: string;
      password: string;
    }) => {
      if (!auth) {
        throw new Error('Firebase auth is not initialized');
      }

      const userCredential = await signInWithEmailAndPassword(
        auth,
        email,
        password
      );
      const user = userCredential.user;

      // Use centralized session establishment
      await establishSession();

      return {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        emailVerified: user.emailVerified,
      };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: authQueryKeys.all });
    },
  });
};

export const useCustomTokenSignIn = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (customToken: string) => {
      if (!auth) {
        throw new Error('Firebase auth is not initialized');
      }

      const userCredential = await signInWithCustomToken(auth, customToken);
      const user = userCredential.user;
      const idToken = await user.getIdToken();

      await sessionLogin({ idToken });
      resetAuthMode();

      return {
        uid: user.uid,
        email: user.email,
        displayName: user.displayName,
        photoURL: user.photoURL,
        emailVerified: user.emailVerified,
      };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: authQueryKeys.all });
    },
  });
};

export const refreshSession = async (): Promise<boolean> => {
  try {
    console.log(auth, 'Refreshing session...');
    if (!auth) return false;

    const currentUser = auth.currentUser;
    console.log(currentUser, 'Current user for session refresh');
    if (!currentUser) return false;

    // Use centralized session establishment with force refresh
    return await establishSession();
  } catch (_error) {
    return false;
  }
};

export const useSignOut = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      await logout();
      if (auth) {
        await signOut(auth);
      }
    },
    onSuccess: () => {
      queryClient.clear();
    },
  });
};

export const useGetOwnProfile = () => {
  return useQuery({
    queryKey: authQueryKeys.user(),
    queryFn: async () => {
      if (!auth) {
        throw new Error('Firebase auth is not initialized');
      }

      const user = auth.currentUser;
      if (!user) {
        throw new Error('User not authenticated');
      }

      // const idToken = await user.getIdToken();
      return await getOwnProfile();
    },
    enabled: !!auth?.currentUser,
    retry: 3,
  });
};

export const useVerifySession = () => {
  const { isAuthenticated, user } = useFirebaseAuth();

  return useQuery({
    queryKey: authQueryKeys.session(),
    queryFn: async () => {
      if (!isAuthenticated || !user) {
        console.error('Error → User not authenticated', {
          isAuthenticated,
          user,
        });
        throw new Error('User not authenticated');
      }

      return await verifySession();
    },
    enabled: isAuthenticated && !!user,
    retry: 3,
    refetchInterval: 10 * 60 * 1000,
    refetchOnWindowFocus: false,
  });
};
