import React, { ReactNode } from 'react';
import { AuthProvider as AuthContextProvider } from '@minicardiac-client/apis';
import SessionVerifier from '../components/session-verifier';

interface AuthProviderProps {
  children: ReactNode;
}

/**
 * Simplified AuthProvider - Middleware handles route-level authentication
 * SessionVerifier now only handles session sync, not primary auth logic
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  return (
    <AuthContextProvider>
      <SessionVerifier />
      {children}
    </AuthContextProvider>
  );
};
